/**
 * 主应用入口文件
 * 负责应用初始化、模块加载和全局事件管理
 */

// 导入核心模块
import { Store } from './state/store.js';
import { MessageComponent } from './components/message.js';
import { ToastComponent } from './components/toast.js';
import { SettingsComponent } from './components/settings.js';
import { globalComponentFactory } from './components/base.js';
import { EventManager } from './utils/events.js';
import { DOMUtils } from './utils/dom.js';
import { StorageUtils } from './utils/storage.js';
import { globalThemeManager } from './utils/theme.js';
import { globalToolbarNotifications, ToolbarActions } from './components/toolbar-notifications.js';

/**
 * 主应用类
 */
class ChatApp {
  constructor() {
    this.store = new Store();
    this.eventManager = new EventManager();
    this.components = new Map();
    this.isInitialized = false;
    
    // 绑定方法上下文
    this.handleMessage = this.handleMessage.bind(this);
    this.handleKeydown = this.handleKeydown.bind(this);
    this.handleResize = this.handleResize.bind(this);
  }

  /**
   * 初始化应用
   */
  async init() {
    if (this.isInitialized) {
      console.warn('App already initialized');
      return;
    }

    try {
      console.log('Initializing Chat App...');
      
      // 初始化存储
      await this.initializeStorage();
      
      // 初始化组件
      await this.initializeComponents();
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 应用初始设置
      this.applySettings();
      
      // 标记为已初始化
      this.isInitialized = true;
      
      console.log('Chat App initialized successfully');
      
      // // 显示欢迎消息
      // this.showWelcomeMessage();
      
    } catch (error) {
      console.error('Failed to initialize app:', error);
      this.showError('应用初始化失败，请刷新页面重试');
    }
  }

  /**
   * 初始化存储
   */
  async initializeStorage() {
    // 加载用户设置
    const settings = StorageUtils.getSettings();
    this.store.setState('settings', settings);
    
    // 加载聊天历史（如果需要）
    const chatHistory = StorageUtils.getChatHistory();
    this.store.setState('messages', chatHistory);
  }

  /**
   * 初始化组件
   */
  async initializeComponents() {
    // 注册组件到工厂
    globalComponentFactory.register('message', MessageComponent);
    globalComponentFactory.register('toast', ToastComponent);
    globalComponentFactory.register('settings', SettingsComponent);

    // 初始化Toast组件
    const toastComponent = new ToastComponent();
    this.components.set('toast', toastComponent);

    // 初始化消息组件
    const messageComponent = new MessageComponent(this.store, this.eventManager);
    this.components.set('message', messageComponent);

    // 初始化设置组件
    const settingsComponent = new SettingsComponent(this.store, this.eventManager);
    this.components.set('settings', settingsComponent);

    // 初始化主题管理器
    globalThemeManager.init();

    // 初始化所有组件
    for (const [componentName, component] of this.components) {
      try {
        await component.init();
        console.log(`Component ${componentName} initialized`);
      } catch (error) {
        console.error(`Failed to initialize component ${componentName}:`, error);
      }
    }
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // VS Code消息监听
    window.addEventListener('message', this.handleMessage);

    // 键盘事件
    document.addEventListener('keydown', this.handleKeydown);

    // 窗口大小变化
    window.addEventListener('resize', this.handleResize);

    // 发送按钮点击事件
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
      sendBtn.addEventListener('click', () => this.sendMessage());
    }

    // 应用状态变化监听
    this.store.subscribe('settings', (settings) => {
      this.applySettings(settings);
    });

    // 组件间通信
    this.eventManager.on('message:send', this.handleSendMessage.bind(this));
    this.eventManager.on('message:edit', this.handleEditMessage.bind(this));
    this.eventManager.on('message:regenerate-ai-reply', this.handleRegenerateAIReply.bind(this));
    this.eventManager.on('settings:show', this.handleShowSettings.bind(this));
    this.eventManager.on('toast:show', this.handleShowToast.bind(this));
  }

  /**
   * 处理来自VS Code的消息
   */
  handleMessage(event) {
    const message = event.data;
    console.log('Received message:', message.type);
    
    switch (message.type) {
      case 'newChat':
        this.handleNewChat();
        break;
      case 'clearChat':
        this.handleClearChat();
        break;
      case 'exportChat':
        this.handleExportChat();
        break;
      case 'showSettings':
        this.handleShowSettings();
        break;
      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  /**
   * 处理键盘事件
   */
  handleKeydown(event) {
    // Ctrl/Cmd + Enter 发送消息
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
      event.preventDefault();
      this.sendMessage();
    }

    // Escape 关闭模态框
    if (event.key === 'Escape') {
      this.eventManager.emit('modal:close');
    }
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 通知组件窗口大小变化
    this.eventManager.emit('window:resize');
  }

  /**
   * 应用设置
   */
  applySettings(settings = null) {
    const currentSettings = settings || this.store.getState('settings');
    
    // 应用主题
    document.documentElement.setAttribute('data-theme', currentSettings.theme);
    
    // 应用字号
    document.documentElement.setAttribute('data-font-size', currentSettings.fontSize);
    
    // 应用其他设置
    if (currentSettings.autoScroll !== undefined) {
      this.store.setState('autoScroll', currentSettings.autoScroll);
    }
  }

  /**
   * 显示欢迎消息
   */
  showWelcomeMessage() {
    const messageComponent = this.components.get('message');
    if (messageComponent) {
      messageComponent.addMessage({
        content: '👋 您好！我是您的AI助手。今天我能为您做些什么？',
        isUser: false,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 处理新建聊天
   */
  handleNewChat() {
    const messageComponent = this.components.get('message');
    if (!messageComponent) {
      return;
    }

    // 如果没有消息，直接创建新聊天
    if (messageComponent.messages.length === 0) {
      this.showWelcomeMessage();
      this.showToast('新聊天已创建', 'success');
      return;
    }

    // 显示确认对话框
    globalToolbarNotifications.notifyConfirm(
      ToolbarActions.RESET,
      () => {
        // 确认创建新聊天
        this.performNewChat();
      },
      () => {
        // 取消创建新聊天
        globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '新建聊天操作已取消');
      },
      `确定要创建新聊天吗？当前 ${messageComponent.messages.length} 条消息将被清空，此操作不可撤销。`
    );
  }

  /**
   * 执行新建聊天
   */
  performNewChat() {
    const messageComponent = this.components.get('message');
    if (messageComponent) {
      const messageCount = messageComponent.messages.length;
      messageComponent.clearMessages();
      this.showWelcomeMessage();
      this.showToast(`新聊天已创建，已清空 ${messageCount} 条消息`, 'success');
    }
  }

  /**
   * 处理清空聊天
   */
  handleClearChat() {
    const messageComponent = this.components.get('message');
    if (messageComponent) {
      messageComponent.clearMessages();
      this.showToast('聊天记录已清空', 'success');
    }
  }

  /**
   * 处理导出聊天
   */
  handleExportChat() {
    const messageComponent = this.components.get('message');
    if (messageComponent) {
      const messages = this.store.getState('messages');
      const exportData = messageComponent.exportMessages(messages);
      
      // 发送到VS Code进行保存
      window.parent.postMessage({
        type: 'exportChatData',
        data: exportData
      }, '*');
      
      this.showToast('聊天记录已导出', 'success');
    }
  }

  /**
   * 处理显示设置
   */
  handleShowSettings() {
    const settingsComponent = this.components.get('settings');
    if (settingsComponent) {
      settingsComponent.show();
    }
  }

  /**
   * 发送消息
   */
  sendMessage() {
    const messageTextarea = document.getElementById('messageTextarea');
    if (!messageTextarea) {
      return;
    }

    const content = messageTextarea.value.trim();
    if (!content) {
      return;
    }

    // 创建消息数据
    const messageData = {
      content: content,
      isUser: true,
      timestamp: Date.now()
    };

    // 清空输入框
    messageTextarea.value = '';

    // 发送消息
    this.handleSendMessage(messageData);
  }

  /**
   * 处理发送消息
   */
  handleSendMessage(messageData) {
    const messageComponent = this.components.get('message');
    if (messageComponent) {
      messageComponent.addMessage(messageData);

      // 如果是用户消息，生成AI回复
      if (messageData.isUser) {
        this.generateAIReply(messageData.content);
      }
    }
  }

  /**
   * 处理重新生成AI回复
   */
  handleRegenerateAIReply(data) {
    this.generateAIReply(data.content);
  }

  /**
   * 生成AI回复（模拟）
   */
  generateAIReply(userMessage) {
    // 模拟AI思考时间
    setTimeout(() => {
      const aiReplies = [
        '我理解您的问题。让我来帮助您解决这个问题。',
        '这是一个很好的问题！根据我的理解...',
        '感谢您的提问。我建议您可以尝试以下方法：',
        '我很乐意为您提供帮助。关于这个问题...',
        '这确实是一个值得思考的问题。我的建议是...',
        '根据您的描述，我认为最好的解决方案是...',
        '让我为您分析一下这个情况...',
        '我明白您的需求。这里有几个可能的解决方案：'
      ];

      // 根据用户消息内容选择更合适的回复
      let reply;
      const lowerMessage = userMessage.toLowerCase();

      if (lowerMessage.includes('你好') || lowerMessage.includes('hello')) {
        reply = '您好！我是AI助手，很高兴为您服务。有什么我可以帮助您的吗？';
      } else if (lowerMessage.includes('谢谢') || lowerMessage.includes('感谢')) {
        reply = '不客气！如果您还有其他问题，随时可以问我。';
      } else if (lowerMessage.includes('帮助') || lowerMessage.includes('help')) {
        reply = '我很乐意帮助您！请告诉我您遇到了什么具体问题，我会尽力为您提供解决方案。';
      } else if (lowerMessage.includes('代码') || lowerMessage.includes('编程')) {
        reply = '关于编程问题，我可以为您提供代码示例、最佳实践建议或帮助调试。请告诉我您需要什么具体的帮助。';
      } else {
        // 随机选择一个通用回复
        reply = aiReplies[Math.floor(Math.random() * aiReplies.length)];
      }

      const messageComponent = this.components.get('message');
      if (messageComponent) {
        messageComponent.addMessage({
          content: reply,
          isUser: false,
          timestamp: Date.now()
        });
      }
    }, 1000 + Math.random() * 2000); // 1-3秒的随机延迟
  }

  /**
   * 处理编辑消息
   */
  handleEditMessage(messageData) {
    const messageComponent = this.components.get('message');
    if (messageComponent) {
      messageComponent.editMessage(messageData);
    }
  }

  /**
   * 显示Toast通知
   */
  handleShowToast(data) {
    this.showToast(data.message, data.type);
  }

  /**
   * 显示Toast通知
   */
  showToast(message, type = 'info') {
    const toastComponent = this.components.get('toast');
    if (toastComponent) {
      toastComponent.show(message, type);
    }
  }

  /**
   * 显示错误信息
   */
  showError(message) {
    this.showToast(message, 'error');
  }

  /**
   * 销毁应用
   */
  destroy() {
    // 移除事件监听
    window.removeEventListener('message', this.handleMessage);
    document.removeEventListener('keydown', this.handleKeydown);
    window.removeEventListener('resize', this.handleResize);
    
    // 销毁组件
    for (const [, component] of this.components) {
      if (component.destroy) {
        component.destroy();
      }
    }
    
    // 清理状态
    this.components.clear();
    this.isInitialized = false;
    
    console.log('Chat App destroyed');
  }
}

// 创建全局应用实例
const app = new ChatApp();

// DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

// 导出应用实例（用于调试）
window.chatApp = app;

export default app;
