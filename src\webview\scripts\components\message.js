/**
 * 消息组件
 * 负责消息的显示、编辑、折叠等功能
 */

import BaseComponent from './base.js';
import { DOMUtils } from '../utils/dom.js';
import { StorageUtils } from '../utils/storage.js';
import MessageManager from './message-manager.js';
import { globalToolbarNotifications, ToolbarActions } from './toolbar-notifications.js';
import { globalAnimationManager, AnimationTypes, MicroInteractions } from '../utils/animations.js';

/**
 * 消息组件类
 */
export class MessageComponent extends BaseComponent {
  constructor(store, eventManager) {
    const messagesContainer = document.getElementById('messages');
    super(messagesContainer, {
      className: 'message-component',
      autoInit: true
    });
    
    this.store = store;
    this.globalEventManager = eventManager;
    this.messages = [];
    this.autoScroll = true;

    // 初始化消息管理器
    this.messageManager = new MessageManager(store, eventManager);
    
    // 绑定方法上下文
    this.addMessage = this.addMessage.bind(this);
    this.editMessage = this.handleEditMessage.bind(this);
    this.deleteMessage = this.handleDeleteMessage.bind(this);
    this.clearMessages = this.clearMessages.bind(this);
    this.exportMessages = this.exportMessages.bind(this);
  }

  /**
   * 获取初始状态
   */
  getInitialState() {
    return {
      ...super.getInitialState(),
      messageCount: 0,
      isTyping: false
    };
  }

  /**
   * 初始化后钩子
   */
  async afterInit() {
    // 监听全局事件
    this.globalEventManager.on('message:add', this.addMessage);
    this.globalEventManager.on('message:edit', this.editMessage);
    this.globalEventManager.on('message:delete', this.deleteMessage);
    this.globalEventManager.on('message:clear', this.clearMessages);
    
    // 监听状态变化
    this.store.subscribe('messages', (messages) => {
      this.messages = messages;
      this.setState('messageCount', messages.length);
    });
    
    this.store.subscribe('autoScroll', (autoScroll) => {
      this.autoScroll = autoScroll;
    });
    
    // 设置消息容器
    this.setupMessageContainer();

    // 加载历史消息
    this.loadMessages();
  }

  /**
   * 设置消息容器
   */
  setupMessageContainer() {
    if (!this.element) {return;}

    // 如果容器有messages-wrapper子元素，使用它作为实际的消息容器
    const wrapper = this.element.querySelector('.messages-wrapper');
    if (wrapper) {
      this.messagesContainer = wrapper;
      wrapper.className = 'messages-list';
    } else {
      this.element.className = 'messages-list';
      this.messagesContainer = this.element;
    }
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (!this.element) {return;}
    
    // 使用事件委托处理消息操作
    this.element.addEventListener('click', (e) => {
      const target = e.target;
      
      // 编辑按钮
      if (target.classList.contains('message-edit-btn')) {
        const messageElement = target.closest('.message');
        const messageId = messageElement.dataset.messageId;
        this.handleEditMessage(messageId);
      }
      
      // 删除按钮
      if (target.classList.contains('message-delete-btn')) {
        const messageElement = target.closest('.message');
        const messageId = messageElement.dataset.messageId;
        this.handleDeleteMessage(messageId);
      }
      
      // 展开/收起按钮
      if (target.classList.contains('message-expand-btn')) {
        const messageElement = target.closest('.message');
        this.toggleMessageExpansion(messageElement);
      }
      
      // 复制按钮
      if (target.classList.contains('message-copy-btn')) {
        const messageElement = target.closest('.message');
        this.copyMessage(messageElement);
      }
    });

    // 为交互元素添加微交互效果
    this.setupMicroInteractions();
  }

  /**
   * 设置微交互效果
   */
  setupMicroInteractions() {
    // 为所有按钮添加悬停和波纹效果
    const buttons = this.element.querySelectorAll('.btn, .message-action-btn, .message-expand-btn');
    buttons.forEach(button => {
      MicroInteractions.addHoverEffect(button, { scale: 1.05 });
      MicroInteractions.addRippleEffect(button);
      MicroInteractions.addFocusRing(button);
    });

    // 为消息内容添加悬停效果
    const messageContents = this.element.querySelectorAll('.message-content');
    messageContents.forEach(content => {
      MicroInteractions.addHoverEffect(content, { scale: 1.01 });
    });
  }

  /**
   * 执行渲染
   */
  async doRender() {
    if (!this.messagesContainer) {return;}

    // 清空容器
    this.messagesContainer.innerHTML = '';

    // 渲染所有消息
    this.messages.forEach(message => {
      const messageElement = this.createMessageElement(message);
      this.messagesContainer.appendChild(messageElement);
    });

    // 设置微交互效果
    this.setupMicroInteractions();

    // 自动滚动到底部
    if (this.autoScroll) {
      this.scrollToBottom();
    }
  }

  /**
   * 加载消息
   */
  loadMessages() {
    const messages = this.store.getState('messages') || [];
    this.messages = messages;
    this.render();
  }

  /**
   * 添加消息
   * @param {object} messageData - 消息数据
   */
  addMessage(messageData) {
    const message = {
      id: messageData.id || this.generateMessageId(),
      content: messageData.content,
      isUser: messageData.isUser || false,
      timestamp: messageData.timestamp || Date.now(),
      edited: false,
      collapsed: false,
      versions: [{
        content: messageData.content,
        timestamp: messageData.timestamp || Date.now(),
        aiReply: null // 将在AI回复时设置
      }],
      currentVersionIndex: 0
    };

    this.messages.push(message);
    this.store.setState('messages', this.messages);

    // 添加到存储
    StorageUtils.addMessageToHistory(message);

    // 创建并添加消息元素
    const messageElement = this.createMessageElement(message);
    this.messagesContainer.appendChild(messageElement);

    // 添加进入动画
    this.animateMessageEntry(messageElement, message.isUser);

    // 检查是否需要折叠
    this.checkMessageCollapse(messageElement);

    // 自动滚动
    if (this.autoScroll) {
      this.scrollToBottom();
    }

    this.emit('messageAdded', message);
  }

  /**
   * 创建消息元素
   * @param {object} message - 消息对象
   * @returns {Element} 消息元素
   */
  createMessageElement(message) {
    const messageDiv = DOMUtils.createElement('div', {
      className: `message ${message.isUser ? 'message-user' : 'message-ai'}`,
      'data-message-id': message.id
    });
    
    const messageContent = DOMUtils.createElement('div', {
      className: 'message-content'
    });
    
    // 消息文本
    const messageText = DOMUtils.createElement('div', {
      className: 'message-text',
      textContent: message.content
    });
    
    // 消息操作按钮
    const messageActions = DOMUtils.createElement('div', {
      className: 'message-actions'
    });
    
    // 只为用户消息添加编辑按钮和版本控制
    if (message.isUser) {
      // 版本切换控件（如果有多个版本）
      if (message.versions && message.versions.length > 1) {
        const versionControls = this.createVersionControls(message);
        messageActions.appendChild(versionControls);
      }

      const editBtn = DOMUtils.createElement('button', {
        className: 'btn btn-icon message-edit-btn',
        title: '编辑消息',
        innerHTML: '✏️'
      });
      messageActions.appendChild(editBtn);
    }
    
    // 复制按钮
    const copyBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon message-copy-btn',
      title: '复制消息',
      innerHTML: '📋'
    });
    messageActions.appendChild(copyBtn);
    
    // 删除按钮
    const deleteBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon message-delete-btn',
      title: '删除消息',
      innerHTML: '🗑️'
    });
    messageActions.appendChild(deleteBtn);
    
    messageContent.appendChild(messageText);
    messageContent.appendChild(messageActions);
    
    // 时间戳和编辑信息
    const messageTime = DOMUtils.createElement('div', {
      className: 'message-time'
    });

    let timeText = this.formatTime(message.timestamp);
    if (message.edited && message.editedAt) {
      timeText += ` (已编辑 ${this.formatTime(message.editedAt)})`;
    }
    messageTime.textContent = timeText;
    
    messageDiv.appendChild(messageContent);
    messageDiv.appendChild(messageTime);
    
    return messageDiv;
  }

  /**
   * 检查消息是否需要折叠
   * @param {Element} messageElement - 消息元素
   */
  checkMessageCollapse(messageElement) {
    const messageText = messageElement.querySelector('.message-text');
    if (!messageText) {return;}

    // 使用更准确的行数检测方法
    const lineCount = this.calculateLineCount(messageText);

    // 如果超过5行，添加折叠功能
    if (lineCount > 5) {
      this.addCollapseFeature(messageElement, lineCount);
    }
  }

  /**
   * 计算文本行数
   * @param {Element} textElement - 文本元素
   * @returns {number} 行数
   */
  calculateLineCount(textElement) {
    // 保存原始样式
    const originalStyle = {
      height: textElement.style.height,
      maxHeight: textElement.style.maxHeight,
      overflow: textElement.style.overflow
    };

    // 临时设置样式以获取真实高度
    textElement.style.height = 'auto';
    textElement.style.maxHeight = 'none';
    textElement.style.overflow = 'visible';

    // 获取计算后的样式
    const computedStyle = getComputedStyle(textElement);
    const lineHeight = parseFloat(computedStyle.lineHeight);
    const paddingTop = parseFloat(computedStyle.paddingTop);
    const paddingBottom = parseFloat(computedStyle.paddingBottom);

    // 计算内容高度
    const contentHeight = textElement.scrollHeight - paddingTop - paddingBottom;

    // 恢复原始样式
    textElement.style.height = originalStyle.height;
    textElement.style.maxHeight = originalStyle.maxHeight;
    textElement.style.overflow = originalStyle.overflow;

    // 计算行数
    const lineCount = Math.ceil(contentHeight / lineHeight);

    return lineCount;
  }

  /**
   * 添加折叠功能
   * @param {Element} messageElement - 消息元素
   * @param {number} lineCount - 行数
   */
  addCollapseFeature(messageElement, lineCount) {
    const messageText = messageElement.querySelector('.message-text');
    const messageContent = messageElement.querySelector('.message-content');

    // 添加折叠样式
    DOMUtils.addClass(messageText, 'message-collapsed');

    // 创建展开按钮
    const expandBtn = DOMUtils.createElement('button', {
      className: 'message-expand-btn',
      textContent: '展开',
      title: `显示完整内容 (${lineCount} 行)`
    });

    // 创建折叠指示器
    const indicator = DOMUtils.createElement('div', {
      className: 'message-collapse-indicator',
      textContent: `${lineCount}行`
    });

    // 添加到消息中
    messageContent.appendChild(indicator);
    messageElement.insertBefore(expandBtn, messageElement.querySelector('.message-time'));

    // 标记消息为可折叠
    messageElement.dataset.collapsible = 'true';
    messageElement.dataset.lineCount = lineCount;
  }

  /**
   * 切换消息展开/收起
   * @param {Element} messageElement - 消息元素
   */
  toggleMessageExpansion(messageElement) {
    const messageText = messageElement.querySelector('.message-text');
    const expandBtn = messageElement.querySelector('.message-expand-btn');
    const indicator = messageElement.querySelector('.message-collapse-indicator');
    const lineCount = messageElement.dataset.lineCount || '多';

    const isCollapsed = DOMUtils.hasClass(messageText, 'message-collapsed');

    if (isCollapsed) {
      // 展开消息
      this.expandMessage(messageElement, messageText, expandBtn, indicator);
    } else {
      // 收起消息
      this.collapseMessage(messageElement, messageText, expandBtn, indicator, lineCount);
    }

    // 触发事件
    this.emit('messageToggled', {
      messageId: messageElement.dataset.messageId,
      expanded: !isCollapsed
    });
  }

  /**
   * 展开消息
   * @param {Element} messageElement - 消息元素
   * @param {Element} messageText - 消息文本元素
   * @param {Element} expandBtn - 展开按钮
   * @param {Element} indicator - 指示器
   */
  expandMessage(messageElement, messageText, expandBtn, indicator) {
    // 获取完整高度
    const fullHeight = this.getFullHeight(messageText);

    // 设置当前高度为起始点
    messageText.style.maxHeight = '5.5em';

    // 添加展开状态类
    DOMUtils.addClass(messageElement, 'message-expanded');
    DOMUtils.removeClass(messageText, 'message-collapsed');

    // 动画到完整高度
    requestAnimationFrame(() => {
      messageText.style.maxHeight = fullHeight + 'px';
    });

    // 动画完成后移除内联样式
    setTimeout(() => {
      messageText.style.maxHeight = '';
    }, 300);

    // 更新按钮和指示器
    expandBtn.textContent = '收起';
    expandBtn.title = '收起内容';
    if (indicator) {
      indicator.style.opacity = '0';
    }
  }

  /**
   * 收起消息
   * @param {Element} messageElement - 消息元素
   * @param {Element} messageText - 消息文本元素
   * @param {Element} expandBtn - 展开按钮
   * @param {Element} indicator - 指示器
   * @param {string} lineCount - 行数
   */
  collapseMessage(messageElement, messageText, expandBtn, indicator, lineCount) {
    // 获取当前高度
    const currentHeight = messageText.scrollHeight;

    // 设置当前高度为起始点
    messageText.style.maxHeight = currentHeight + 'px';

    // 移除展开状态类
    DOMUtils.removeClass(messageElement, 'message-expanded');

    // 动画到折叠高度
    requestAnimationFrame(() => {
      messageText.style.maxHeight = '5.5em';
      DOMUtils.addClass(messageText, 'message-collapsed');
    });

    // 动画完成后移除内联样式
    setTimeout(() => {
      messageText.style.maxHeight = '';
    }, 300);

    // 更新按钮和指示器
    expandBtn.textContent = '展开';
    expandBtn.title = `显示完整内容 (${lineCount} 行)`;
    if (indicator) {
      indicator.style.opacity = '1';
    }
  }

  /**
   * 获取元素的完整高度
   * @param {Element} element - 元素
   * @returns {number} 完整高度
   */
  getFullHeight(element) {
    // 保存当前样式
    const originalMaxHeight = element.style.maxHeight;
    const originalOverflow = element.style.overflow;

    // 临时移除限制
    element.style.maxHeight = 'none';
    element.style.overflow = 'visible';

    // 获取完整高度
    const fullHeight = element.scrollHeight;

    // 恢复样式
    element.style.maxHeight = originalMaxHeight;
    element.style.overflow = originalOverflow;

    return fullHeight;
  }

  /**
   * 处理编辑消息
   * @param {string} messageId - 消息ID
   */
  handleEditMessage(messageId) {
    const message = this.messages.find(m => m.id === messageId);
    if (!message) {return;}

    const messageElement = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) {return;}

    // 检查是否已经在编辑模式
    if (messageElement.classList.contains('editing')) {
      return;
    }

    // 进入编辑模式
    this.enterEditMode(messageElement, message);

    // 显示编辑模式提示
    // globalToolbarNotifications.notifyInfo(ToolbarActions.EDIT, '进入编辑模式，Ctrl+Enter保存，Esc取消');

    this.emit('messageEdit', message);
  }

  /**
   * 进入编辑模式
   * @param {Element} messageElement - 消息元素
   * @param {object} message - 消息对象
   */
  enterEditMode(messageElement, message) {
    const messageText = messageElement.querySelector('.message-text');
    if (!messageText) {return;}

    // 标记为编辑状态
    messageElement.classList.add('editing');

    // 保存原始内容
    const originalContent = message.content;
    messageElement.dataset.originalContent = originalContent;

    // 创建编辑器
    const editor = this.createInlineEditor(originalContent);

    // 替换文本内容
    messageText.style.display = 'none';
    messageText.parentNode.insertBefore(editor, messageText);

    // 创建编辑控制按钮
    const editControls = this.createEditControls(messageElement);
    messageText.parentNode.insertBefore(editControls, messageText);

    // 聚焦编辑器
    const textarea = editor.querySelector('.message-editor');
    textarea.focus();
    textarea.setSelectionRange(textarea.value.length, textarea.value.length);

    // 调整编辑器高度
    this.adjustEditorHeight(textarea);
  }

  /**
   * 创建内联编辑器
   * @param {string} content - 初始内容
   * @returns {Element} 编辑器元素
   */
  createInlineEditor(content) {
    const editorContainer = DOMUtils.createElement('div', {
      className: 'message-editor-container'
    });

    const textarea = DOMUtils.createElement('textarea', {
      className: 'message-editor input',
      value: content,
      placeholder: '编辑消息内容...'
    });

    // 绑定键盘事件
    textarea.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        this.saveEdit(textarea.closest('.message'));
      } else if (e.key === 'Escape') {
        e.preventDefault();
        this.cancelEdit(textarea.closest('.message'));
      }
    });

    // 绑定输入事件
    textarea.addEventListener('input', () => {
      this.adjustEditorHeight(textarea);
    });

    editorContainer.appendChild(textarea);
    return editorContainer;
  }

  /**
   * 创建编辑控制按钮
   * @param {Element} messageElement - 消息元素
   * @returns {Element} 控制按钮容器
   */
  createEditControls(messageElement) {
    const controls = DOMUtils.createElement('div', {
      className: 'message-edit-controls'
    });

    const saveBtn = DOMUtils.createElement('button', {
      className: 'btn btn-primary btn-sm',
      textContent: '保存',
      title: 'Ctrl+Enter 保存'
    });

    const cancelBtn = DOMUtils.createElement('button', {
      className: 'btn btn-secondary btn-sm',
      textContent: '取消',
      title: 'Esc 取消'
    });

    // 绑定事件
    saveBtn.addEventListener('click', () => this.saveEdit(messageElement));
    cancelBtn.addEventListener('click', () => this.cancelEdit(messageElement));

    controls.appendChild(saveBtn);
    controls.appendChild(cancelBtn);

    return controls;
  }

  /**
   * 保存编辑
   * @param {Element} messageElement - 消息元素
   */
  saveEdit(messageElement) {
    const messageId = messageElement.dataset.messageId;
    const editor = messageElement.querySelector('.message-editor');
    const newContent = editor.value.trim();

    if (!newContent) {
      globalToolbarNotifications.notifyWarning(ToolbarActions.EDIT, '消息内容不能为空');
      return;
    }

    // 更新消息内容
    const messageIndex = this.messages.findIndex(m => m.id === messageId);
    if (messageIndex !== -1) {
      const message = this.messages[messageIndex];
      const currentVersion = message.versions[message.currentVersionIndex];

      // 如果内容没有变化，直接退出编辑模式
      if (newContent === currentVersion.content) {
        this.exitEditMode(messageElement);
        return;
      }

      // 创建新版本
      const newVersion = {
        content: newContent,
        timestamp: Date.now(),
        aiReply: null
      };

      // 添加新版本到版本数组
      message.versions.push(newVersion);
      message.currentVersionIndex = message.versions.length - 1;
      message.content = newContent; // 更新当前显示的内容
      message.edited = true;
      message.editedAt = Date.now();

      // 更新store
      this.store.setState('messages', this.messages);

      // 更新存储
      StorageUtils.saveChatHistory(this.messages);

      // 退出编辑模式并更新显示
      this.exitEditMode(messageElement, newContent);

      // 如果是第一次编辑（从1个版本变成2个版本），添加版本控件
      if (message.versions.length === 2) {
        this.addVersionControlsToMessage(messageElement, message);
      } else if (message.versions.length > 2) {
        // 更新现有版本控件
        this.updateVersionControls(messageElement, message);
      }

      // 显示成功提示
      // globalToolbarNotifications.notifySuccess(ToolbarActions.EDIT, `消息已更新 (版本 ${message.versions.length})`);

      // 如果是用户消息，触发AI重新回复
      if (message.isUser) {
        this.regenerateAIReply(message);
      }

      this.emit('messageUpdated', message);
    }
  }

  /**
   * 取消编辑
   * @param {Element} messageElement - 消息元素
   */
  cancelEdit(messageElement) {
    this.exitEditMode(messageElement);
    // globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '编辑已取消');
  }

  /**
   * 重新生成AI回复
   * @param {object} message - 用户消息对象
   */
  regenerateAIReply(message) {
    // 找到当前用户消息在数组中的位置
    const userMessageIndex = this.messages.findIndex(m => m.id === message.id);

    if (userMessageIndex !== -1) {
      // 删除该用户消息之后的所有AI回复（通常只有一个）
      const messagesToRemove = [];
      for (let i = userMessageIndex + 1; i < this.messages.length; i++) {
        if (!this.messages[i].isUser) {
          messagesToRemove.push(i);
        } else {
          break; // 遇到下一个用户消息就停止
        }
      }

      // 从后往前删除，避免索引变化
      for (let i = messagesToRemove.length - 1; i >= 0; i--) {
        const indexToRemove = messagesToRemove[i];
        const aiReplyElement = this.messagesContainer.querySelector(`[data-message-id="${this.messages[indexToRemove].id}"]`);
        if (aiReplyElement) {
          aiReplyElement.remove();
        }
        this.messages.splice(indexToRemove, 1);
      }
    }

    // 触发新的AI回复
    this.globalEventManager.emit('message:regenerate-ai-reply', {
      userMessage: message,
      content: message.content
    });
  }

  /**
   * 创建版本切换控件
   * @param {object} message - 消息对象
   * @returns {Element} 版本控件元素
   */
  createVersionControls(message) {
    const versionControls = DOMUtils.createElement('div', {
      className: 'message-version-controls'
    });

    const prevBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon version-btn',
      title: '上一个版本',
      innerHTML: '◀'
    });

    const versionInfo = DOMUtils.createElement('span', {
      className: 'version-info',
      textContent: `${message.currentVersionIndex + 1} / ${message.versions.length}`
    });

    const nextBtn = DOMUtils.createElement('button', {
      className: 'btn btn-icon version-btn',
      title: '下一个版本',
      innerHTML: '▶'
    });

    // 设置按钮禁用状态
    if (message.currentVersionIndex === 0) {
      prevBtn.disabled = true;
      prevBtn.setAttribute('disabled', 'disabled');
    }

    if (message.currentVersionIndex === message.versions.length - 1) {
      nextBtn.disabled = true;
      nextBtn.setAttribute('disabled', 'disabled');
    }

    // 绑定事件
    prevBtn.addEventListener('click', () => this.switchVersion(message, -1));
    nextBtn.addEventListener('click', () => this.switchVersion(message, 1));

    versionControls.appendChild(prevBtn);
    versionControls.appendChild(versionInfo);
    versionControls.appendChild(nextBtn);

    return versionControls;
  }

  /**
   * 切换消息版本
   * @param {object} message - 消息对象
   * @param {number} direction - 方向 (-1: 上一个, 1: 下一个)
   */
  switchVersion(message, direction) {
    const newIndex = message.currentVersionIndex + direction;

    if (newIndex < 0 || newIndex >= message.versions.length) {
      return;
    }

    // 更新当前版本索引
    message.currentVersionIndex = newIndex;
    const currentVersion = message.versions[newIndex];
    message.content = currentVersion.content;

    // 更新消息显示
    const messageElement = this.messagesContainer.querySelector(`[data-message-id="${message.id}"]`);
    if (messageElement) {
      const messageText = messageElement.querySelector('.message-text');
      if (messageText) {
        messageText.textContent = currentVersion.content;
      }

      // 更新版本控件
      this.updateVersionControls(messageElement, message);
    }

    // 更新存储
    this.store.setState('messages', this.messages);
    StorageUtils.saveChatHistory(this.messages);

    // 如果是用户消息，触发AI重新回复
    if (message.isUser) {
      this.regenerateAIReply(message);
    }

    // 显示版本切换提示
    // globalToolbarNotifications.notifyInfo(ToolbarActions.EDIT, `已切换到版本 ${newIndex + 1}`);
  }

  /**
   * 为消息添加版本控件
   * @param {Element} messageElement - 消息元素
   * @param {object} message - 消息对象
   */
  addVersionControlsToMessage(messageElement, message) {
    const messageActions = messageElement.querySelector('.message-actions');
    const editBtn = messageActions.querySelector('.message-edit-btn');

    if (messageActions && editBtn) {
      const versionControls = this.createVersionControls(message);
      messageActions.insertBefore(versionControls, editBtn);
    }
  }

  /**
   * 更新版本控件
   * @param {Element} messageElement - 消息元素
   * @param {object} message - 消息对象
   */
  updateVersionControls(messageElement, message) {
    const versionControls = messageElement.querySelector('.message-version-controls');
    if (versionControls) {
      const versionInfo = versionControls.querySelector('.version-info');
      const prevBtn = versionControls.querySelector('.version-btn:first-child');
      const nextBtn = versionControls.querySelector('.version-btn:last-child');

      if (versionInfo) {
        versionInfo.textContent = `${message.currentVersionIndex + 1} / ${message.versions.length}`;
      }

      if (prevBtn) {
        const shouldDisablePrev = message.currentVersionIndex === 0;
        prevBtn.disabled = shouldDisablePrev;

        if (shouldDisablePrev) {
          prevBtn.setAttribute('disabled', 'disabled');
        } else {
          prevBtn.removeAttribute('disabled');
        }
      }

      if (nextBtn) {
        const shouldDisableNext = message.currentVersionIndex === message.versions.length - 1;
        nextBtn.disabled = shouldDisableNext;

        if (shouldDisableNext) {
          nextBtn.setAttribute('disabled', 'disabled');
        } else {
          nextBtn.removeAttribute('disabled');
        }
      }
    }
  }

  /**
   * 退出编辑模式
   * @param {Element} messageElement - 消息元素
   * @param {string} newContent - 新内容（可选）
   */
  exitEditMode(messageElement, newContent = null) {
    const messageText = messageElement.querySelector('.message-text');
    const editorContainer = messageElement.querySelector('.message-editor-container');
    const editControls = messageElement.querySelector('.message-edit-controls');

    // 移除编辑器和控制按钮
    if (editorContainer) {
      editorContainer.remove();
    }
    if (editControls) {
      editControls.remove();
    }

    // 更新文本内容
    if (newContent !== null) {
      messageText.textContent = newContent;
    }

    // 显示原始文本
    messageText.style.display = '';

    // 移除编辑状态
    messageElement.classList.remove('editing');
    delete messageElement.dataset.originalContent;
  }

  /**
   * 调整编辑器高度
   * @param {Element} textarea - 文本域元素
   */
  adjustEditorHeight(textarea) {
    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    const maxHeight = window.innerHeight * 0.3; // 最大30%屏幕高度

    if (scrollHeight > maxHeight) {
      textarea.style.height = maxHeight + 'px';
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.height = Math.max(scrollHeight, 60) + 'px'; // 最小60px
      textarea.style.overflowY = 'hidden';
    }
  }

  /**
   * 处理删除消息
   * @param {string} messageId - 消息ID
   */
  handleDeleteMessage(messageId) {
    const messageIndex = this.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) {return;}

    // 显示确认对话框
    globalToolbarNotifications.notifyConfirm(
      ToolbarActions.DELETE,
      () => {
        // 确认删除
        this.performDeleteMessage(messageId, messageIndex);
      },
      () => {
        // 取消删除
        globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '删除操作已取消');
      },
      '确定要删除这条消息吗？此操作不可撤销。'
    );
  }

  /**
   * 执行删除消息
   * @param {string} messageId - 消息ID
   * @param {number} messageIndex - 消息索引
   */
  performDeleteMessage(messageId, messageIndex) {
    try {
      // 从数组中移除
      const deletedMessage = this.messages.splice(messageIndex, 1)[0];

      // 更新存储
      this.store.setState('messages', this.messages);
      StorageUtils.saveChatHistory(this.messages);

      // 从DOM中移除（带动画）
      const messageElement = this.messagesContainer.querySelector(`[data-message-id="${messageId}"]`);
      if (messageElement) {
        this.animateMessageExit(messageElement).then(() => {
          messageElement.remove();
        });
      }

      // 显示成功通知
      globalToolbarNotifications.notifySuccess(ToolbarActions.DELETE, '消息已删除');

      this.emit('messageDeleted', deletedMessage);
    } catch (error) {
      console.error('Failed to delete message:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.DELETE, '删除失败，请重试');
    }
  }

  /**
   * 复制消息
   * @param {Element} messageElement - 消息元素
   */
  async copyMessage(messageElement) {
    const messageText = messageElement.querySelector('.message-text');
    if (!messageText) {return;}

    try {
      await DOMUtils.copyToClipboard(messageText.textContent);
      globalToolbarNotifications.notifySuccess(ToolbarActions.COPY);
    } catch (error) {
      console.error('Failed to copy message:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.COPY, '复制失败，请重试');
    }
  }

  /**
   * 清空消息
   */
  clearMessages() {
    if (this.messages.length === 0) {
      globalToolbarNotifications.notifyInfo(ToolbarActions.DELETE, '没有消息需要清空');
      return;
    }

    // 显示确认对话框
    globalToolbarNotifications.notifyConfirm(
      ToolbarActions.RESET,
      () => {
        // 确认清空
        this.performClearMessages();
      },
      () => {
        // 取消清空
        globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '清空操作已取消');
      },
      `确定要清空所有 ${this.messages.length} 条消息吗？此操作不可撤销。`
    );
  }

  /**
   * 执行清空消息
   */
  performClearMessages() {
    try {
      const messageCount = this.messages.length;

      this.messages = [];
      this.store.setState('messages', []);
      StorageUtils.clearChatHistory();

      if (this.messagesContainer) {
        this.messagesContainer.innerHTML = '';
      }

      globalToolbarNotifications.notifySuccess(ToolbarActions.RESET, `已清空 ${messageCount} 条消息`);

      this.emit('messagesCleared');
    } catch (error) {
      console.error('Failed to clear messages:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.RESET, '清空失败，请重试');
    }
  }

  /**
   * 导出消息
   * @param {Array} messages - 消息数组（可选）
   * @returns {string} 导出的文本
   */
  exportMessages(messages = null) {
    const messagesToExport = messages || this.messages;

    if (messagesToExport.length === 0) {
      globalToolbarNotifications.notifyWarning(ToolbarActions.EXPORT, '没有消息可以导出');
      return '';
    }

    try {
      // 显示导出中通知
      const loadingId = globalToolbarNotifications.notifyLoading(ToolbarActions.EXPORT, '正在导出聊天记录...');

      let exportData = '聊天记录导出\n';
      exportData += `导出时间: ${new Date().toLocaleString('zh-CN')}\n\n`;

      messagesToExport.forEach(message => {
        const time = this.formatTime(message.timestamp);
        const sender = message.isUser ? '用户' : 'AI';
        exportData += `[${time}] ${sender}: ${message.content}\n`;
      });

      // 模拟导出延迟
      setTimeout(() => {
        globalToolbarNotifications.hide(loadingId);
        globalToolbarNotifications.notifySuccess(ToolbarActions.EXPORT, `成功导出 ${messagesToExport.length} 条消息`);
      }, 500);

      return exportData;
    } catch (error) {
      console.error('Export failed:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.EXPORT, '导出失败，请重试');
      return '';
    }
  }

  /**
   * 消息进入动画
   * @param {Element} messageElement - 消息元素
   * @param {boolean} isUser - 是否为用户消息
   */
  animateMessageEntry(messageElement, isUser) {
    // 检查是否应该减少动画
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      return;
    }

    // 根据消息类型选择不同的动画
    const animationType = isUser ? AnimationTypes.SLIDE_IN_RIGHT : AnimationTypes.SLIDE_IN_LEFT;

    // 设置初始状态
    messageElement.style.opacity = '0';
    messageElement.style.transform = isUser ? 'translateX(50px)' : 'translateX(-50px)';

    // 执行动画
    globalAnimationManager.animate(messageElement, animationType, {
      duration: 300,
      easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      onComplete: () => {
        // 清除内联样式，让CSS接管
        messageElement.style.opacity = '';
        messageElement.style.transform = '';
      }
    }).catch(() => {
      // 动画失败时确保元素可见
      messageElement.style.opacity = '';
      messageElement.style.transform = '';
    });
  }

  /**
   * 消息退出动画
   * @param {Element} messageElement - 消息元素
   * @returns {Promise} 动画完成的Promise
   */
  animateMessageExit(messageElement) {
    // 检查是否应该减少动画
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      return Promise.resolve();
    }

    // 执行退出动画
    return globalAnimationManager.animate(messageElement, AnimationTypes.FADE_OUT, {
      duration: 200,
      easing: 'ease-in'
    }).catch(() => {
      // 动画失败时也要继续
      return Promise.resolve();
    });
  }

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    // 滚动的是外层的messages-container，而不是内层的messages-wrapper
    const scrollContainer = this.element;
    if (scrollContainer) {
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
    }
  }

  /**
   * 格式化时间
   * @param {number} timestamp - 时间戳
   * @returns {string} 格式化的时间
   */
  formatTime(timestamp) {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * 生成消息ID
   * @returns {string} 消息ID
   */
  generateMessageId() {
    return `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 搜索消息
   * @param {string} query - 搜索查询
   * @param {object} options - 搜索选项
   * @returns {Array} 搜索结果
   */
  searchMessages(query, options = {}) {
    if (!query || !query.trim()) {
      globalToolbarNotifications.notifyWarning(ToolbarActions.SEARCH, '请输入搜索关键词');
      return [];
    }

    try {
      // 显示搜索中通知
      const loadingId = globalToolbarNotifications.notifyLoading(ToolbarActions.SEARCH, '正在搜索消息...');

      // 执行搜索
      const results = this.messageManager.searchMessages(query, options);

      // 隐藏加载通知
      setTimeout(() => {
        globalToolbarNotifications.hide(loadingId);

        // 显示搜索结果
        if (results.length === 0) {
          globalToolbarNotifications.notifyInfo(ToolbarActions.SEARCH, `未找到包含"${query}"的消息`);
        } else {
          globalToolbarNotifications.notifySuccess(ToolbarActions.SEARCH, `找到 ${results.length} 条匹配的消息`);
        }
      }, 300); // 模拟搜索延迟

      return results;
    } catch (error) {
      console.error('Search failed:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.SEARCH, '搜索失败，请重试');
      return [];
    }
  }

  /**
   * 获取消息统计
   * @returns {object} 统计信息
   */
  getStatistics() {
    return this.messageManager.getStatistics();
  }

  /**
   * 批量删除消息
   * @param {Array} messageIds - 消息ID数组
   * @returns {number} 删除的消息数量
   */
  batchDeleteMessages(messageIds) {
    if (!messageIds || messageIds.length === 0) {
      globalToolbarNotifications.notifyWarning(ToolbarActions.DELETE, '没有选择要删除的消息');
      return 0;
    }

    // 显示确认对话框
    globalToolbarNotifications.notifyConfirm(
      ToolbarActions.DELETE,
      () => {
        // 确认批量删除
        this.performBatchDelete(messageIds);
      },
      () => {
        // 取消批量删除
        globalToolbarNotifications.notifyInfo(ToolbarActions.CANCEL, '批量删除已取消');
      },
      `确定要删除选中的 ${messageIds.length} 条消息吗？此操作不可撤销。`
    );

    return 0; // 实际删除在确认后执行
  }

  /**
   * 执行批量删除
   * @param {Array} messageIds - 消息ID数组
   */
  performBatchDelete(messageIds) {
    try {
      const total = messageIds.length;
      let completed = 0;
      let failed = 0;

      // 显示进度通知
      globalToolbarNotifications.notifyBatch('删除', total, completed, failed);

      // 模拟批量处理过程
      const processNext = () => {
        if (completed + failed < total) {
          const currentId = messageIds[completed + failed];

          try {
            // 尝试删除单个消息
            const messageIndex = this.messages.findIndex(m => m.id === currentId);
            if (messageIndex !== -1) {
              this.messages.splice(messageIndex, 1);

              // 从DOM中移除
              const messageElement = this.messagesContainer.querySelector(`[data-message-id="${currentId}"]`);
              if (messageElement) {
                messageElement.remove();
              }

              completed++;
            } else {
              failed++;
            }
          } catch (error) {
            console.error(`Failed to delete message ${currentId}:`, error);
            failed++;
          }

          // 更新进度
          globalToolbarNotifications.notifyBatch('删除', total, completed, failed);

          // 继续处理下一个
          setTimeout(processNext, 50);
        } else {
          // 批量删除完成
          this.finalizeBatchDelete(total, completed, failed);
        }
      };

      // 开始处理
      processNext();

    } catch (error) {
      console.error('Batch delete failed:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.DELETE, '批量删除失败，请重试');
    }
  }

  /**
   * 完成批量删除
   * @param {number} total - 总数
   * @param {number} completed - 完成数
   * @param {number} failed - 失败数
   */
  finalizeBatchDelete(total, completed, failed) {
    // 更新存储
    this.store.setState('messages', this.messages);
    StorageUtils.saveChatHistory(this.messages);

    // 显示最终结果
    if (failed === 0) {
      globalToolbarNotifications.notifySuccess(ToolbarActions.DELETE, `成功删除 ${completed} 条消息`);
    } else if (completed === 0) {
      globalToolbarNotifications.notifyError(ToolbarActions.DELETE, `删除失败，${failed} 条消息无法删除`);
    } else {
      globalToolbarNotifications.notifyWarning(ToolbarActions.DELETE, `删除完成：成功 ${completed} 条，失败 ${failed} 条`);
    }

    this.emit('batchDeleted', { total, completed, failed });
  }

  /**
   * 高亮搜索结果
   * @param {string} query - 搜索查询
   */
  highlightSearchResults(query) {
    if (!query.trim()) {
      this.clearHighlights();
      return;
    }

    const messageElements = this.messagesContainer.querySelectorAll('.message-text');
    messageElements.forEach(element => {
      const text = element.textContent;
      const highlightedText = text.replace(
        new RegExp(`(${query})`, 'gi'),
        '<mark>$1</mark>'
      );
      element.innerHTML = highlightedText;
    });
  }

  /**
   * 清除高亮
   */
  clearHighlights() {
    const messageElements = this.messagesContainer.querySelectorAll('.message-text');
    messageElements.forEach(element => {
      const text = element.textContent;
      element.innerHTML = '';
      element.textContent = text;
    });
  }

  /**
   * 导出消息（增强版）
   * @param {string} format - 导出格式
   * @param {Array} messageIds - 要导出的消息ID（可选）
   * @returns {string} 导出的数据
   */
  exportMessagesAdvanced(format = 'json', messageIds = null) {
    try {
      let messagesToExport;

      if (messageIds) {
        messagesToExport = this.messages.filter(m => messageIds.includes(m.id));
        if (messagesToExport.length === 0) {
          globalToolbarNotifications.notifyWarning(ToolbarActions.EXPORT, '没有选中的消息可以导出');
          return '';
        }
      } else {
        messagesToExport = this.messages;
        if (messagesToExport.length === 0) {
          globalToolbarNotifications.notifyWarning(ToolbarActions.EXPORT, '没有消息可以导出');
          return '';
        }
      }

      // 显示导出中通知
      const loadingId = globalToolbarNotifications.notifyLoading(ToolbarActions.EXPORT, `正在导出${format.toUpperCase()}格式...`);

      const result = messageIds
        ? this.messageManager.exportMessages(format, messagesToExport)
        : this.messageManager.exportMessages(format);

      // 模拟导出延迟
      setTimeout(() => {
        globalToolbarNotifications.hide(loadingId);
        globalToolbarNotifications.notifySuccess(ToolbarActions.EXPORT, `成功导出 ${messagesToExport.length} 条消息为${format.toUpperCase()}格式`);
      }, 800);

      return result;
    } catch (error) {
      console.error('Advanced export failed:', error);
      globalToolbarNotifications.notifyError(ToolbarActions.EXPORT, `${format.toUpperCase()}格式导出失败，请重试`);
      return '';
    }
  }

  /**
   * 销毁前钩子
   */
  beforeDestroy() {
    // 移除全局事件监听
    this.globalEventManager.off('message:add', this.addMessage);
    this.globalEventManager.off('message:edit', this.editMessage);
    this.globalEventManager.off('message:delete', this.deleteMessage);
    this.globalEventManager.off('message:clear', this.clearMessages);
  }
}

export default MessageComponent;
